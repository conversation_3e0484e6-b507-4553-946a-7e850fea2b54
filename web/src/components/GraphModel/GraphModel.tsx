import { useCallback, useEffect, useState, useMemo, memo } from 'react'
import React<PERSON>low, {
  Handle,
  Position,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  getBezierPath,
} from 'reactflow'
import { useLazyQuery } from '@apollo/client'
import {
  Tooltip,
  Button,
  Drawer,
  Form,
  Input,
  Select,
  Descriptions,
  message,
  Spin,
  InputNumber,
  notification,
  Radio,
  Tag,
  Row,
  Col,
  Checkbox,
  Collapse,
} from 'antd'
import {
  InboxOutlined,
  PercentageOutlined,
  EnvironmentOutlined,
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons'
import dagre from 'dagre'
import 'reactflow/dist/style.css'
import LoadingSkeleton from '../LoadingSkeleton/LoadingSkeleton'
import { fetchPlacesAutoComplete } from 'web/src/components/AddProduct/AddProduct'
import './style.css'

import {
  CREATE_EMISSIONS_FACTOR_MUTATION,
  CREATE_SUPPLIER_MUTATION,
  CREATE_PRODUCT_MUTATION,
  CREATE_PRODUCT_PROCESS_MODEL_MUTATION,
  BACKUP_PRODUCT_MUTATION,
  RESTORE_PRODUCT_MUTATION,
  DELETE_PRODUCT_MUTATION,
  ACTIVATE_PRODUCT_MUTATION,
  PREDICT_EMISSIONS_FACTORS_QUERY,
  PREDICT_INGREDIENT_SOURCE_QUERY,
} from 'src/utils/graphql'

import { useMutation } from '@redwoodjs/web'
import countryCodeLookup from 'country-code-lookup'
import EmissionsFactorSelector from '../EmissionsFactorSelector/EmissionsFactorSelector'
import { renderImpactFactorUnit } from 'src/utils/helper'
import { NumberInput } from '@tremor/react'

const nodeTypeColors = {
  material: '#b7eb8f',
  bundle: '#ffd591',
  transportation: '#91d5ff',
  production: '#ffd591',
  use: '#efdbff',
  eol: '#ffa39e',
}

const typeIcons = {
  material: '/images/icons/ingredients.png',
  packaging: '/images/icons/packaging.png',
  bundle: '/images/icons/manufacturing.png',
  transportation: '/images/icons/transport.png',
  production: '/images/icons/manufacturing.png',
  use: '/images/icons/consumer_use.png',
  eol: '/images/icons/eol.png',
}

const nodeWidth = 280
const nodeHeight = 100

const nodeTypeRules = {
  material: {
    minInputs: 0,
    maxInputs: 1,
    minOutputs: 1,
    maxOutputs: 1,
  },
  bundle: {
    minInputs: 1,
    maxInputs: 100,
    minOutputs: 0,
    maxOutputs: 1,
  },
  transportation: {
    minInputs: 1,
    maxInputs: 1,
    minOutputs: 1,
    maxOutputs: 1,
  },
  production: {
    minInputs: 1,
    maxInputs: 100,
    minOutputs: 0,
    maxOutputs: 100,
  },
  use: {
    minInputs: 1,
    maxInputs: 1,
    minOutputs: 0,
    maxOutputs: 1000,
  },
  eol: {
    minInputs: 1,
    maxInputs: 1,
    minOutputs: 0,
    maxOutputs: 0,
  },
}

const getLayoutedElements = (nodes, edges) => {
  const dagreGraph = new dagre.graphlib.Graph()
  dagreGraph.setDefaultEdgeLabel(() => ({}))

  dagreGraph.setGraph({
    rankdir: 'LR', //left to right
    ranksep: 250,
    nodesep: 200,
    edgesep: 100,
    marginx: 50,
    marginy: 50,
    acyclicer: 'greedy',
    ranker: 'network-simplex',
  })

  nodes.forEach((node) => {
    dagreGraph.setNode(node.id, { width: nodeWidth, height: nodeHeight })
  })

  edges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target)
  })

  dagre.layout(dagreGraph)

  const layoutedNodes = nodes.map((node) => {
    const nodeWithPosition = dagreGraph.node(node.id)
    return {
      ...node,
      position: {
        x: nodeWithPosition.x - nodeWidth / 2,
        y: nodeWithPosition.y - nodeHeight / 2,
      },
      draggable: true,
    }
  })

  return { nodes: layoutedNodes, edges }
}

const findEmissionsData = (nodeName, nodeType, walkerData) => {
  if (!walkerData) return null

  //handle material node type because of discrepancy in naming. TODO: fix this in the backend
  let categoryKey =
    nodeType === 'material' ? 'materials' : nodeType.toLowerCase()

  try {
    const category = walkerData[categoryKey]
    if (!category || !category.emissions) return null

    return category.emissions.find((e) => e.name === nodeName)
  } catch (error) {
    console.error('Error finding emissions data:', error)
    return null
  }
}

const formatLocation = (city, country) => {
  return city ? `${city}, ${country}` : country
}

const CustomEdge = ({
  id,
  source,
  target,
  sourceX,
  sourceY,
  targetX,
  targetY,
  selected,
  markerEnd,
  style,
  data,
}) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  })
  const [isHovered, setIsHovered] = useState(false)

  return (
    <>
      <path
        id={id}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          ...style,
          stroke: selected ? '#1890ff' : style.stroke,
          strokeWidth: selected ? 5 : style.strokeWidth,
          strokeDasharray: isHovered ? 'none' : '10 5',
          animation: isHovered ? 'none' : 'flowAnimation 1s linear infinite',
          animated: isHovered ? true : false,
        }}
      />
      {selected && data?.context?.editProcessModelEnabled ? (
        <foreignObject
          width={30}
          height={30}
          x={labelX - 15}
          y={labelY - 15}
          className="edgebutton-foreignobject"
        >
          <Button
            type="primary"
            size="small"
            danger
            icon={<DeleteOutlined />}
            style={{
              borderRadius: '50%',
              minWidth: '30px',
              height: '30px',
              padding: '0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onClick={(event) => {
              event.stopPropagation()
              data?.onDelete(id)
            }}
          />
        </foreignObject>
      ) : null}
    </>
  )
}

const _CustomNode = memo(({ data }) => {
  const [isHovered, setIsHovered] = useState(false)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [isAddNodeDrawerOpen, setIsAddNodeDrawerOpen] = useState(false)
  const [isViewMode, setIsViewMode] = useState(false)
  const [typingTimeout, setTypingTimeout] = useState(null)
  const [addNodeForm] = Form.useForm()
  const [editNodeForm] = Form.useForm()

  const [mapboxAutocompleteOptions, setMapboxAutocompleteOptions] = useState([])

  const [nodeLocationMapboxDataLoading, setNodeLocationMapboxDataLoading] =
    useState(false)

  const [selectedNodeType, setSelectedNodeType] = useState(null)
  const [nodeUnits, setNodeUnits] = useState([
    {
      label: 'Grams (g)',
      value: 'g',
    },
    {
      label: 'Kilograms (kg)',
      value: 'kg',
    },
    {
      label: 'Liters (l)',
      value: 'l',
    },
  ])

  const [
    emissionsFactorMatchesDrawerIsOpen,
    setEmissionsFactorActivityMatchesDrawerIsOpen,
  ] = useState(false)

  const [selectedEmissionsFactor, setSelectedEmissionsFactor] = useState(null)

  const [selectedChemicalName, setSelectedChemicalName] = useState({
    chemicalName: null,
    casNo: null,
    activityType: null,
    unit: null,
    geography: null,
    currentEmissionsFactor: null,
    emissionsFactorMatches: null,
  })

  const [
    predictEmissionsFactors,
    { loading: predictEmissionsFactorsIsLoading },
  ] = useLazyQuery(PREDICT_EMISSIONS_FACTORS_QUERY)

  const [
    predictIngredientSource,
    { loading: predictIngredientSourceIsLoading },
  ] = useLazyQuery(PREDICT_INGREDIENT_SOURCE_QUERY)

  const [
    advancedFieldsCollapseKey,
    setAdvancedFieldsCollapseKey,
  ] = useState(['0'])

  const [
    massAllocationIsChecked,
    setMassAllocationIsChecked,
  ] = useState(false)

  const mapNodeTypeToLifecycleStage = (nodeType) => {
    const nodeTypeToStageMap = {
      'material': 'Raw Materials',
      'packaging': 'Packaging',
      'production': 'Manufacturing',
      'transportation': 'Transportation',
      'use': 'Use Phase',
      'eol': 'End-of-Life',
      'bundle': 'Manufacturing',  // Bundle is part of manufacturing
    }
    return nodeTypeToStageMap[nodeType] || null
  }

  const handlePredictEmissionsFactors = async ({
    chemicalName,
    productCategory = null,
    casNo = null,
    geography,
    geographyModeling = false,
    unit = null,
    lcaLifecycleStage = null,
  }) => {
    const emissionsFactorMatch = {
      activityMatch: null,
      activityMatches: [],
    }

    const response = await predictEmissionsFactors({
      variables: {
        chemicalName: chemicalName,
        productCategory: productCategory,
        casNo: casNo,
        geography: geography,
        geographyModeling: geographyModeling,
        unit: unit,
        lcaLifecycleStage: lcaLifecycleStage,
      },
    })

    if (response?.data?.predictEmissionsFactors?.recommendations?.length) {
      emissionsFactorMatch.activityMatch = {
        ...(response?.data?.predictEmissionsFactors?.matchedActivity ??
          emissionsFactorMatch.activityMatches[0]),
        ...(response?.data?.predictEmissionsFactors?.confidence && {
          confidence: response.data.predictEmissionsFactors.confidence,
        }),
        ...(response?.data?.predictEmissionsFactors?.explanation && {
          explanation: response.data.predictEmissionsFactors.explanation,
        }),
        exchanges:
          response.data.predictEmissionsFactors?.matchedActivity?.exchanges ??
          [],
      }

      emissionsFactorMatch.activityMatches =
        response?.data?.predictEmissionsFactors.recommendations.map(
          (activity) => {
            const activityMatch = {
              ...activity,
              chemicalName: chemicalName,
            }

            if (
              activity.activityName ===
                emissionsFactorMatch.activityMatch.activityName &&
              activity.referenceProduct ===
                emissionsFactorMatch.activityMatch.referenceProduct &&
              activity.source === emissionsFactorMatch.activityMatch.source &&
              activity.geography ===
                emissionsFactorMatch.activityMatch.geography
            ) {
              activityMatch.confidence =
                response.data.predictEmissionsFactors?.confidence
              activityMatch.explanation =
                response.data.predictEmissionsFactors?.explanation
            }

            return { ...activityMatch, chemicalName: chemicalName }
          }
        )
    }
    return emissionsFactorMatch
  }

  const handleSelectEmissionsFactor = async () => {
    setEmissionsFactorActivityMatchesDrawerIsOpen(true)
  }

  const handleEmissionsFactorEdit = async () => {
    const country = await editNodeForm
      .getFieldValue('location')
      ?.split(',')
      .slice(-1)[0]
      ?.trim()

    const countryInfo = countryCodeLookup.byCountry(country)
    const nodeType = await editNodeForm.getFieldValue('type')

    // Debug logging to understand nodeType in edit mode
    console.log('handleEmissionsFactorEdit nodeType:', nodeType)

    setSelectedChemicalName({
      chemicalName: await editNodeForm.getFieldValue('name'),
      casNo: null,
      activityType: nodeType, // Use actual node type instead of hardcoded 'Raw Material'
      unit: (await editNodeForm.getFieldValue('unit')) ?? 'kg',
      geography: countryInfo?.iso3 ?? 'GLO',
      currentEmissionsFactor: selectedEmissionsFactor,
      emissionsFactorMatches: [],
    })
    setEmissionsFactorActivityMatchesDrawerIsOpen(true)
  }

  const handlePredictMaterialSource = async (material) => {
    const countryInfo = countryCodeLookup.byCountry(data.location?.country)

    const predictedIngredientSource = {
      country: data.location?.country,
      district: data.location?.city ?? null,
      country_code: countryInfo.iso3,
      predicted: false,
    }

    try {
      const response = await predictIngredientSource({
        variables: {
          ingredientName: material,
          productCategory: null,
          country: data.location?.country,
        },
      })

      if (response?.data?.predictIngredientSource) {
        predictedIngredientSource.country =
          response.data.predictIngredientSource.country
        predictedIngredientSource.district = null
        predictedIngredientSource.country_code =
          response.data.predictIngredientSource.countryCode
        predictedIngredientSource.predicted = true
      }
    } catch (error) {
      console.error('Error predicting ingredient source:', error)
    } finally {
      return predictedIngredientSource
    }
  }

  //TODO: replace this with API
  const getUnitOptions = (nodeType) => {
    switch (nodeType) {
      case 'material':
      case 'packaging':
        return [
          { label: 'Grams (g)', value: 'g' },
          { label: 'Kilograms (kg)', value: 'kg' },
          { label: 'Liters (l)', value: 'l' },
        ]
      case 'use':
        return [
          { label: 'Kilograms (kg)', value: 'kg' },
          { label: 'Kilowatt Hour(kWh)', value: 'kWh' },
          { label: 'Liters (l)', value: 'l' },
        ]
      default:
        return [
          { label: 'Grams (g)', value: 'g' },
          { label: 'Kilograms (kg)', value: 'kg' },
          { label: 'Liters (l)', value: 'l' },
          { label: 'Kilowatt Hour(kWh)', value: 'kWh' },
        ]
    }
  }

  const handlePredictEmissionsFactor = async () => {
    const selectedForm = isAddNodeDrawerOpen ? addNodeForm : editNodeForm

    const nodeType = await selectedForm.getFieldValue('type')

    if (
      !['material', 'packaging', 'production', 'use', 'eol'].includes(nodeType)
    )
      return

    const country = await selectedForm
      .getFieldValue('location')
      ?.split(',')
      .slice(-1)[0]
      ?.trim()

    const countryInfo = countryCodeLookup.byCountry(country)

    const matches = await handlePredictEmissionsFactors({
      chemicalName: await selectedForm.getFieldValue('name'),
      geography: countryInfo?.iso3 ?? 'GLO',
      geographyModeling: true,
      unit: (await selectedForm.getFieldValue('unit')) ?? 'kg',
      lcaLifecycleStage: mapNodeTypeToLifecycleStage(nodeType),
    })

    setSelectedEmissionsFactor(matches?.activityMatch)
    
    // Debug logging to understand nodeType being passed
    console.log('GraphModel nodeType:', nodeType, 'being set as activityType')
    
    setSelectedChemicalName({
      chemicalName: await selectedForm.getFieldValue('name'),
      casNo: null,
      activityType: nodeType,
      unit: (await selectedForm.getFieldValue('unit')) ?? 'kg',
      geography: countryInfo?.iso3 ?? 'GLO',
      currentEmissionsFactor: matches?.activityMatch,
      emissionsFactorMatches: matches?.activityMatches,
    })
  }

  const handleMaterialNameChange = async (value) => {
    const selectedForm = isAddNodeDrawerOpen ? addNodeForm : editNodeForm

    const nodeType = await selectedForm.getFieldValue('type')

    if (['material', 'packaging'].includes(nodeType)) {
      const predictedIngredientSource = await handlePredictMaterialSource(value)

      const formattedLocation = predictedIngredientSource.district
        ? `${predictedIngredientSource.district}, ${predictedIngredientSource.country}`
        : predictedIngredientSource.country

      selectedForm.setFieldsValue({ location: formattedLocation })
    }

    handlePredictEmissionsFactor()
  }

  const handleUpdateEmissionsFactor = (newEmissionsFactor) => {
    setSelectedEmissionsFactor(newEmissionsFactor)
    setSelectedChemicalName({
      ...selectedChemicalName,
      currentEmissionsFactor: newEmissionsFactor,
    })
    setEmissionsFactorActivityMatchesDrawerIsOpen(false)
    message.success('Emissions factor updated successfully')
  }

  const handleNodeClick = (e) => {
    e.stopPropagation()
    setIsViewMode(true)
    setIsDrawerOpen(true)
  }

  const handleEditClick = (e) => {
    e.stopPropagation()
    setIsViewMode(false)
    editNodeForm.resetFields()
    setMassAllocationIsChecked(data.massAllocationPerKg ? true : false)
    if (data.massAllocationPerKg) {
      setAdvancedFieldsCollapseKey(['1'])
      editNodeForm.setFieldValue('mass-allocation-checkbox', true)
      editNodeForm.setFieldsValue({
        disposal_rate_recycling: data.recyclingDisposalRate,
        disposal_rate_landfill: data.landfillDisposalRate,
        disposal_rate_incineration: data.incinerationDisposalRate,
        disposal_rate_composting: data.compostingDisposalRate,
      })
    }
    editNodeForm.setFieldsValue({
      type: data.nodeType,
    })
    setSelectedEmissionsFactor(data.emissionsFactor ?? null)
    setIsDrawerOpen(true)
  }

  const handleDeleteNode = (e) => {
    e.stopPropagation()
    const { context, id } = data
    const { setNodes, setEdges } = context

    setNodes((nodes) => nodes.filter((node) => node.id !== id))

    setEdges((edges) =>
      edges.filter((edge) => edge.source !== id && edge.target !== id)
    )
  }

  const { context } = data
  const { setNodes, editProcessModelEnabled, userMetadata } = context

  const generateNodeId = (nodes) => {
    const maxId = Math.max(...nodes.map(node => parseInt(node.id)), 0)
    const nodeId = (maxId + 1).toString()
    return nodeId
  }

  const handleAddNode = (values) => {
    try {

      if (massAllocationIsChecked) {
        const totalDisposalRate =
          parseFloat(values.disposal_rate_recycling ?? 0) +
          parseFloat(values.disposal_rate_landfill ?? 0) +
          parseFloat(values.disposal_rate_incineration ?? 0) +
          parseFloat(values.disposal_rate_composting ?? 0)

        if (totalDisposalRate !== 100) {
          throw new Error('Total disposal rate must be 100% when mass allocation is checked')
        }
      }

      data.context.setNodes((nodes) => {
        const nodeId = generateNodeId(nodes)
        const parent = nodes.find((n) => n.id === data.id)
        const newNode = {
          data_id: `${values.type}-${values.name}`,
          id: nodeId,
          type: 'custom',
          position: {
            x: parent.position.x + 250,
            y: parent.position.y,
          },
          draggable: true,
          data: {
            ...values,
            id: nodeId,
            label: values.name,
            nodeType: values.type,
            component: values.component,
            description: values.description,
            massAllocationPerKg: massAllocationIsChecked,
            recyclingDisposalRate: values.disposal_rate_recycling,
            landfillDisposalRate: values.disposal_rate_landfill,
            incinerationDisposalRate: values.disposal_rate_incineration,
            compostingDisposalRate: values.disposal_rate_composting,
            amount: parseFloat(values.amount) || 0,
            quantity: parseFloat(values.quantity) || 1,
            unit: values.unit,
            emissionsFactor: selectedEmissionsFactor
              ? selectedEmissionsFactor
              : null,
            location: values.location
              ? {
                  city: values.location.split(',')[0]?.trim(),
                  country: values.location.split(',').slice(-1)[0]?.trim(),
                }
              : null,
            context: data.context,
          },
        }
        return [...nodes, newNode]
      })

      // data.context.setEdges((edges) => [
      //   ...edges,
      //   {
      //     id: `e-${data.id}-${nodeId}`,
      //     source: data.id,
      //     target: nodeId,
      //     type: 'default',
      //     animated: true,
      //     style: { stroke: '#bfbfbf', strokeWidth: 2, strokeDasharray: '5 5' },
      //     markerEnd: {
      //       type: 'arrowclosed',
      //       width: 15,
      //       height: 15,
      //       color: '#bfbfbf',
      //     },
      //   },
      // ])

      setIsAddNodeDrawerOpen(false)
    } catch (error) {
      console.error('Error adding node:', error)
      message.error('Failed to add node. Error: ' + error.message)
    }
  }

  const handleUpdateNode = async (values) => {
    try {

      if (massAllocationIsChecked) {
        const totalDisposalRate =
          parseFloat(values.disposal_rate_recycling ?? 0) +
          parseFloat(values.disposal_rate_landfill ?? 0) +
          parseFloat(values.disposal_rate_incineration ?? 0) +
          parseFloat(values.disposal_rate_composting ?? 0)

        if (totalDisposalRate !== 100) {
          throw new Error('Total disposal rate must be 100% when mass allocation is checked')
        }
      }
      const location = values.location.split(',')
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === data.id) {
            node.data_id = `${values.type}-${values.name}`;
            const updatedNodeData = {
              ...node.data,
              name: values.name,
              component: values.component,
              description: values.description,
              label: values.name,
              amount: parseFloat(values.amount) || 0,
              quantity: parseFloat(values.quantity) || 0,
              unit: values.unit,
              emissionsFactor: selectedEmissionsFactor
                ? selectedEmissionsFactor
                : null,
              massAllocationPerKg: massAllocationIsChecked,
              recyclingDisposalRate: values.disposal_rate_recycling,
              landfillDisposalRate: values.disposal_rate_landfill,
              incinerationDisposalRate: values.disposal_rate_incineration,
              compostingDisposalRate: values.disposal_rate_composting,
              scrapRate: values.scrap_rate,
              scrapFate: values.scrap_fate,
              location: values.location
                ? {
                    city: location.length === 1 ? null : location[0]?.trim(),
                    country: values.location.split(',').slice(-1)[0]?.trim(),
                  }
                : null,
              recycledContent: values.recycledContent,
            }
            return { ...node, data: updatedNodeData }
          }
          return node
        })
      )
      message.success('Node updated successfully')
      setIsDrawerOpen(false)
    } catch (error) {
      console.error('Error updating node:', error)
      message.error('Failed to update node. Error: ' + error.message)
    }
  }

  const handleSubmit = (values) => {
    handleUpdateNode(values)
  }

  const handleNodeLocationSearch = async (value) => {
    if (!value) {
      return setMapboxAutocompleteOptions([])
    }

    setNodeLocationMapboxDataLoading(true)

    try {
      const placesData = await fetchPlacesAutoComplete(value)
      setMapboxAutocompleteOptions(placesData)
    } catch (error) {
      message.error('Failed to fetch location')
      setMapboxAutocompleteOptions([])
    } finally {
      setNodeLocationMapboxDataLoading(false)
    }
  }


  const walkerEmissions = findEmissionsData(
    data.label,
    data.nodeType,
    data.walkerData
  )

  const ViewModeContent = () => (
    <div className="space-y-4">
      <div className="mb-4 flex items-center justify-between">
        <div className="text-lg font-bold">Node Details</div>
        <Button
          style={{ display: editProcessModelEnabled ? 'block' : 'none' }}
          type="primary"
          icon={<EditOutlined />}
          onClick={handleEditClick}
        >
          Edit
        </Button>
      </div>

      <Descriptions
        bordered
        column={1}
        size="small"
        labelStyle={{ fontWeight: 600 }}
      >
        <Descriptions.Item label="Name">{data.label}</Descriptions.Item>
        {['material', 'packaging'].includes(data.nodeType) ? (
          <Descriptions.Item label="Component">
            {data.component}
          </Descriptions.Item>
        ) : null}
        <Descriptions.Item label="Type">{data.nodeType}</Descriptions.Item>
        {walkerEmissions?.amount ? (
          <Descriptions.Item label="Amount">
            {walkerEmissions?.amount} {walkerEmissions?.unit}
          </Descriptions.Item>
        ) : null}
        {['material', 'packaging'].includes(data.nodeType) ? (
          <Descriptions.Item label="Description">
            {data.description}
          </Descriptions.Item>
        ) : null}
        {['material', 'production'].includes(data.nodeType) ? (
          <Descriptions.Item label="Scrap Rate">
            {data.scrapRate}%
          </Descriptions.Item>
        ) : null}
        {data.nodeType === "use" ? (
          <>
           <Descriptions.Item label="No. of Uses">
            {data.quantity ?? 1}
          </Descriptions.Item>
          {data.amount !== null ? (
          <Descriptions.Item label="Total Amount">
            {data.amount * data.quantity} {data.unit}
          </Descriptions.Item>
        ) : null}
          </>
        ) : null}
        {data.location ? (
          <Descriptions.Item label="Location">
            {formatLocation(data.location?.city, data.location?.country)}
          </Descriptions.Item>
        ) : null}
        {walkerEmissions?.emissionsFactor ? (
          <>
            <Descriptions.Item label="Emissions Factor">
              {walkerEmissions.emissionsFactor.kgCO2e} {renderImpactFactorUnit(userMetadata)}/{walkerEmissions.unit}
            </Descriptions.Item>
            <Descriptions.Item label="Activity">
              {walkerEmissions.emissionsFactor.activityName}
            </Descriptions.Item>
            <Descriptions.Item label="Geography">
              {walkerEmissions.emissionsFactor.geography || 'Global'}
            </Descriptions.Item>
            <Descriptions.Item label="Source">
              {walkerEmissions.emissionsFactor.source}
            </Descriptions.Item>
          </>
        ) : null}
        {walkerEmissions?.totalEmissions ? (
          <Descriptions.Item label="Total Emissions">
            {walkerEmissions?.totalEmissions.toFixed(4)} {renderImpactFactorUnit(userMetadata)}
          </Descriptions.Item>
        ) : null}
      </Descriptions>
    </div>
  )

  const NodeIcon = typeIcons[data.nodeType] ? (
    <img
      style={{ width: 24 }}
      src={typeIcons[data.nodeType]}
      alt={`${data.nodeType} icon`}
    />
  ) : (
    <InboxOutlined />
  )

  return (
    <>
      <div
        id={`${data.nodeType}-${data.label.replace(/\s+/g, '-')}`}
        className="custom-node"
        style={{
          background: nodeTypeColors[data.nodeType] || '#fff',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '10px',
          width: nodeWidth - 20,
          position: 'relative',
          cursor: 'pointer',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleNodeClick}
      >
        {isHovered && editProcessModelEnabled ? (
          <div
            style={{
              position: 'absolute',
              top: -15,
              right: -15,
              zIndex: 10,
              display: 'flex',
              gap: '8px',
            }}
          >
            <Button
              type="primary"
              size="small"
              id='add-node-btn'
              icon={<PlusOutlined />}
              style={{
                borderRadius: '50%',
                minWidth: '30px',
                height: '30px',
                padding: '0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onClick={(e) => {
                e.stopPropagation()
                addNodeForm.resetFields()
                setMassAllocationIsChecked(false)
                setAdvancedFieldsCollapseKey(['0'])
                setSelectedEmissionsFactor(null)
                setSelectedNodeType('material')
                setIsAddNodeDrawerOpen(true)
              }}
            />
            <Button
              type="primary"
              size="small"
              id='edit-node-btn'
              icon={<EditOutlined />}
              style={{
                borderRadius: '50%',
                minWidth: '30px',
                height: '30px',
                padding: '0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onClick={handleEditClick}
            />
            <Button
              type="primary"
              size="small"
              danger
              id='delete-node-btn'
              icon={<DeleteOutlined />}
              style={{
                borderRadius: '50%',
                minWidth: '30px',
                height: '30px',
                padding: '0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onClick={handleDeleteNode}
            />
          </div>
        ) : null}

        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {NodeIcon}
            <div style={{ fontWeight: 'bold' }}>{data.label}</div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            {data.location ? (
              <Tooltip
                title={formatLocation(
                  data.location?.city,
                  data.location?.country
                )}
              >
                <Button
                  type="text"
                  size="small"
                  icon={<EnvironmentOutlined />}
                  style={{ minWidth: 'auto', padding: '0 4px' }}
                />
              </Tooltip>
            ) : null}
          </div>
        </div>

        <div style={{ fontSize: '12px', marginTop: '8px' }}>
          {data.amount && !isNaN(data.amount) ? (
            <div>
              <span style={{ fontWeight: '600' }}>Amount:</span>{' '}
              {data.amount.toFixed(4)} {data.unit}
            </div>
          ) : null}
          {walkerEmissions?.totalEmissions ? (
            <div>
              <span style={{ fontWeight: '600' }}>Emissions:</span>{' '}
              {walkerEmissions.totalEmissions.toFixed(4)} {renderImpactFactorUnit(userMetadata)}
            </div>
          ) : null}
        </div>

        <Handle type="target" position={Position.Left} />
        <Handle type="source" position={Position.Right} />
      </div>

      <Drawer
        title={
          isViewMode
            ? `Node Details: ${data.label}`
            : `Edit Node: ${data.label}`
        }
        placement="right"
        onClose={() => {
          setIsDrawerOpen(false)
          setIsViewMode(false)
        }}
        open={isDrawerOpen}
        width={400}
      >
        {isViewMode ? (
          <ViewModeContent />
        ) : (
          <Form
            id='edit-node-form'
            form={editNodeForm}
            layout="vertical"
            initialValues={{
              name: data.label,
              amount: data.amount,
              component: data.component,
              description: data.description,
              unit: data.unit,
              location: formatLocation(
                data.location?.city,
                data.location?.country
              ),
              quantity: data.quantity ?? 1,
            }}
            onFinish={handleSubmit}
          >
             <Form.Item name="type" hidden>
              <Input />
            </Form.Item>
            <Form.Item name="name" label="Name" rules={[{ required: true }]}>
              <Input
                placeholder='Citric Acid'
                disabled={
                  predictEmissionsFactorsIsLoading ||
                  predictIngredientSourceIsLoading
                }
                onChange={async (e) => {
                  const value = e.target.value
                  if (typingTimeout) {
                    clearTimeout(typingTimeout)
                  }
                  setTypingTimeout(
                    setTimeout(() => {
                      handleMaterialNameChange(value)
                    }, 1500)
                  )
                }}
              />
            </Form.Item>

            {['material', 'packaging'].includes(data.nodeType) ? (
              <Form.Item name="component" label="Component">
                <Input />
              </Form.Item>
            ) : null}

            {!['transportation', 'production', 'bundle'].includes(
              data.nodeType
            ) ? (
              <>
                <Form.Item
                  name="amount"
                  label={data.nodeType === 'use' ? 'Amount (Per Use)' : 'Amount'}
                  rules={[{ required: true }]}
                >
                  <Input type="number" placeholder='150.50'/>
                </Form.Item>

                <Form.Item
                  name="unit"
                  label="Unit"
                  rules={[{ required: true }]}
                >
                  <Select options={getUnitOptions(data.nodeType)} />
                </Form.Item>
              </>
            ) : null}


            {data.nodeType === 'use' ? (
              <Form.Item name="quantity" label="No. of Uses" rules={[{ required: true }]}>
                <NumberInput />
              </Form.Item>
            ) : null}

            {['material', 'packaging'].includes(data.nodeType) ? (
            <>
              <Form.Item
                name="recycledContent"
                label="Recycled Content"
                tooltip="Enter recycled content of the material value in percentage"
              >
                <InputNumber
                  min="0"
                  max="100"
                  placeholder="85"
                  addonBefore={<PercentageOutlined />}
                />
              </Form.Item>
              <Form.Item name="description" label="Description">
              <Input />
              </Form.Item>
            </>
            ) : null}

            <Form.Item
              name="location"
              label="Location"
              rules={[{ required: true }]}
            >
              <Select
                disabled={
                  predictEmissionsFactorsIsLoading ||
                  predictIngredientSourceIsLoading
                }
                showSearch
                suffixIcon={null}
                allowClear
                options={mapboxAutocompleteOptions}
                placeholder="London, United Kingdom"
                onSearch={handleNodeLocationSearch}
                onSelect={(value, option) => {
                  editNodeForm.setFieldsValue({
                    dataField_location: option.data ?? null,
                    location: value,
                  })
                  handlePredictEmissionsFactor()
                }}
                filterOption={false}
                notFoundContent={
                  nodeLocationMapboxDataLoading ? <Spin size="small" /> : null
                }
                onBlur={() => setMapboxAutocompleteOptions([])}
              />
            </Form.Item>
            {data.emissionsFactor ? (
              <>
                <Descriptions
                  bordered
                  column={1}
                  size="small"
                  labelStyle={{ fontWeight: 600 }}
                >
                  <Descriptions.Item label="Emissions Factor">
                    <div className="flex items-center justify-between">
                      <span>
                        {selectedEmissionsFactor?.activityName || 'Not set'}
                      </span>
                      <Button
                        type="link"
                        icon={<EditOutlined />}
                        onClick={handleEmissionsFactorEdit}
                        loading={predictEmissionsFactorsIsLoading}
                      />
                    </div>
                  </Descriptions.Item>
                </Descriptions>
                <br></br>
              </>
            ) : null}

            <Collapse
              style={{ textAlign: 'left' }}
              ghost
              onChange={(key) => {
                setAdvancedFieldsCollapseKey(key)
              }}
              activeKey={advancedFieldsCollapseKey}
              items={[
                {
                  key: '1',
                  label: 'Advanced',
                  children: (
                    <>
                      <Form.Item name="mass-allocation-checkbox" valuePropName="checked">
                        <Checkbox
                          id="mass-allocation-checkbox"
                          onChange={(e) =>
                            setMassAllocationIsChecked(e.target.checked)
                          }
                          checked={massAllocationIsChecked}
                          style={{ float: 'left' }}
                        >
                          Use Mass Allocation
                          <Tooltip title="Use this to allocate material weight based on per kg of product weight. End of life for these nodes would be added right after the next manufacturing node">
                            <Button
                              icon={<InfoCircleOutlined />}
                              type="link"
                              style={{ color: 'grey' }}
                            ></Button>
                          </Tooltip>
                        </Checkbox>

                      </Form.Item>

                      {massAllocationIsChecked && (
                        <>
                        <Form.Item name="disposal_rate_recycling" label="Recycling Rate">
                          <InputNumber
                            min="0"
                            max="100"
                            placeholder="50"
                            addonAfter={<PercentageOutlined />}
                          />
                        </Form.Item>
                        <Form.Item name="disposal_rate_landfill" label="Landfill Rate">
                          <InputNumber
                            min="0"
                            max="100"
                            placeholder="20"
                            addonAfter={<PercentageOutlined />}
                          />
                        </Form.Item>
                        <Form.Item name="disposal_rate_incineration" label="Incineration Rate">
                          <InputNumber
                            min="0"
                            max="100"
                            placeholder="20"
                            addonAfter={<PercentageOutlined />}
                          />
                        </Form.Item>
                        <Form.Item name="disposal_rate_composting" label="Composting Rate">
                          <InputNumber
                            min="0"
                            max="100"
                            placeholder="10"
                            addonAfter={<PercentageOutlined />}
                          />
                        </Form.Item>
                        </>
                      )}
                    </>
                  ),
                },
              ]}
            />

            <Button
              loading={
                predictEmissionsFactorsIsLoading ||
                predictIngredientSourceIsLoading
              }
              type="primary"
              htmlType="submit"
            >
              Update
            </Button>
          </Form>
        )}
      </Drawer>
      <Drawer
        title="Add Connected Node"
        placement="right"
        onClose={() => setIsAddNodeDrawerOpen(false)}
        open={isAddNodeDrawerOpen}
        width={400}
      >
        <Form
          id='add-node-form'
          form={addNodeForm}
          layout="vertical"
          onFinish={handleAddNode}
          initialValues={{
            type: 'material',
            unit: getUnitOptions('material')[0]['value'],
            quantity: 1,
          }}
        >
          <Form.Item name="type" label="Node Type" rules={[{ required: true }]}>
            <Select
              onSelect={(value) => {
                setSelectedNodeType(value)
                addNodeForm.setFieldsValue({ unit: null })
                const units = getUnitOptions(value)
                setNodeUnits(units)
                addNodeForm.setFieldsValue({ unit: units[0].value })
              }}
              options={[
                { label: 'Material', value: 'material' },
                { label: 'Packaging', value: 'packaging' },
                { label: 'Transportation', value: 'transportation' },
                { label: 'Bundle', value: 'bundle' },
                { label: 'Manufacturing', value: 'production' },
                { label: 'Use', value: 'use' },
                { label: 'End of Life', value: 'eol' },
              ]}
            />
          </Form.Item>

          <Form.Item name="name" label="Name" rules={[{ required: true }]}>
            <Input
              placeholder='Citric Acid'
              disabled={
                predictEmissionsFactorsIsLoading ||
                predictIngredientSourceIsLoading
              }
              onChange={(e) => {
                const value = e.target.value
                if (typingTimeout) {
                  clearTimeout(typingTimeout)
                }
                setTypingTimeout(
                  setTimeout(() => {
                    handleMaterialNameChange(value)
                  }, 1500)
                )
              }}
            />
          </Form.Item>

          {['material', 'packaging'].includes(selectedNodeType) ? (
            <Form.Item name="component" label="Component">
              <Input />
            </Form.Item>
          ) : null}

          {!['transportation', 'production', 'bundle', 'eol'].includes(
            selectedNodeType
          ) ? (
            <>
              <Form.Item
                name="amount"
                label={selectedNodeType === 'use' ? 'Amount (Per Use)' : 'Amount'}
                rules={[{ required: true }]}
              >
                <Input type="number" placeholder='150.50' />
              </Form.Item>

              <Form.Item name="unit" label="Unit" rules={[{ required: true }]}>
                <Select options={nodeUnits} />
              </Form.Item>
            </>
          ) : null}

          {['material', 'packaging'].includes(selectedNodeType) ? (
            <Form.Item name="description" label="Description">
              <Input />
            </Form.Item>
          ) : null}

          {selectedNodeType === 'use' ? (
            <Form.Item name="quantity" label="No. of Uses" rules={[{ required: true }]}>
              <NumberInput />
            </Form.Item>
          ) : null}

          <Form.Item
            name="location"
            label="Location"
            rules={[{ required: true }]}
          >
            <Select
              disabled={
                predictEmissionsFactorsIsLoading ||
                predictIngredientSourceIsLoading
              }
              showSearch
              suffixIcon={null}
              allowClear
              options={mapboxAutocompleteOptions}
              placeholder="London, United Kingdom"
              onSearch={handleNodeLocationSearch}
              onSelect={(value, option) => {
                addNodeForm.setFieldsValue({
                  dataField_location: option.data ?? null,
                  location: value,
                })
                handlePredictEmissionsFactor()
              }}
              filterOption={false}
              notFoundContent={
                nodeLocationMapboxDataLoading ? <Spin size="small" /> : null
              }
              onBlur={() => setMapboxAutocompleteOptions([])}
            />
          </Form.Item>

          {!['transportation', 'bundle'].includes(selectedNodeType) ? (
            <>
              <Descriptions
                bordered
                column={1}
                size="small"
                labelStyle={{ fontWeight: 600 }}
              >
                <Descriptions.Item label="Emissions Factor">
                  <div className="flex items-center justify-between">
                    <span>
                      {selectedEmissionsFactor?.activityName ?? 'Not set'}
                    </span>
                    <Button
                      loading={
                        predictEmissionsFactorsIsLoading ||
                        predictIngredientSourceIsLoading
                      }
                      type="link"
                      icon={<EditOutlined />}
                      onClick={handleSelectEmissionsFactor}
                    />
                  </div>
                </Descriptions.Item>
              </Descriptions>
            </>
          ) : null}

          <Collapse
            style={{ textAlign: 'left' }}
            ghost
            onChange={(key) => {
              setAdvancedFieldsCollapseKey(key)
            }}
            activeKey={advancedFieldsCollapseKey}
            items={[
              {
                key: '1',
                label: 'Advanced',
                children: (
                  <>
                    <Form.Item name="mass-allocation-checkbox" valuePropName="checked">
                      <Checkbox
                        id="mass-allocation-checkbox"
                        onChange={(e) =>
                          setMassAllocationIsChecked(e.target.checked)
                        }
                        checked={massAllocationIsChecked}
                        style={{ float: 'left' }}
                      >
                        Mass Allocation Per kg
                        <Tooltip title="Use this to allocate material weight based on per kg of product weight. End of life for these nodes would be added right after the next manufacturing node">
                          <Button
                            icon={<InfoCircleOutlined />}
                            type="link"
                            style={{ color: 'grey' }}
                          ></Button>
                        </Tooltip>
                      </Checkbox>

                    </Form.Item>

                    {massAllocationIsChecked && (
                      <>
                      <Form.Item name="disposal_rate_recycling" label="Recycling Rate">
                        <InputNumber
                          min="0"
                          max="100"
                          placeholder="50"
                          addonAfter={<PercentageOutlined />}
                        />
                      </Form.Item>
                      <Form.Item name="disposal_rate_landfill" label="Landfill Rate">
                        <InputNumber
                          min="0"
                          max="100"
                          placeholder="20"
                          addonAfter={<PercentageOutlined />}
                        />
                      </Form.Item>
                      <Form.Item name="disposal_rate_incineration" label="Incineration Rate">
                        <InputNumber
                          min="0"
                          max="100"
                          placeholder="20"
                          addonAfter={<PercentageOutlined />}
                        />
                      </Form.Item>
                      <Form.Item name="disposal_rate_composting" label="Composting Rate">
                        <InputNumber
                          min="0"
                          max="100"
                          placeholder="10"
                          addonAfter={<PercentageOutlined />}
                        />
                      </Form.Item>
                      </>
                    )}
                  </>
                ),
              },
            ]}
          />

          <Button
            type="primary"
            loading={
              predictEmissionsFactorsIsLoading ||
              predictIngredientSourceIsLoading
            }
            htmlType="submit"
          >
            Add Node
          </Button>
        </Form>
      </Drawer>
      <EmissionsFactorSelector
        isOpen={emissionsFactorMatchesDrawerIsOpen}
        onClose={() => setEmissionsFactorActivityMatchesDrawerIsOpen(false)}
        selectedItem={selectedChemicalName}
        onEmissionsFactorUpdate={handleUpdateEmissionsFactor}
        initialEmissionsFactor={selectedChemicalName.currentEmissionsFactor}
        initialEmissionsFactorMatches={
          selectedChemicalName.emissionsFactorMatches
        }
        editMode={true}
        geographyModelingEnabled={true}
        productCategoryEnabled={true}
        activityType={selectedChemicalName.activityType}
      />
    </>
  )
})

_CustomNode.displayName = 'CustomNode'

const baseCreateNodesAndEdges = (graphData, nodeContext) => {
  if (!graphData || !graphData.nodes || !graphData.edges) {
    return { nodes: [], edges: [] }
  }

  const nodes = graphData.nodes.map((node) => ({
    id: node.id.toString(),
    type: 'custom',
    data: {
      ...node,
      id: node.id.toString(),
      label: node.name,
      nodeType: node.nodeType,
      amount: node.amount ?? 0,
      unit: node.unit,
      location: node.location,
      emissionsFactor: node.emissionsFactor,
      walkerData: graphData.emissions || {},
      context: nodeContext,
    },
  }))

  const edges = graphData.edges.map((edge) => ({
    id: `e-${edge.fromNodeId}-${edge.toNodeId}`,
    source: edge.fromNodeId.toString(),
    target: edge.toNodeId.toString(),
    type: 'default',
    animated: true,
    style: {
      stroke: '#bfbfbf',
      strokeWidth: 5,
      strokeDasharray: '5 5',
    },
    markerEnd: {
      type: 'arrowclosed',
      width: 15,
      height: 15,
      color: '#bfbfbf',
    },
  }))

  return { nodes, edges }
}

const GraphModel = ({ data, orgMemberInfo, userMetadata, loading = false }) => {
  if (loading) return <LoadingSkeleton />
  if (!data) return null

  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])

  const [editProcessModelEnabled, setIsEditProcessModelEnabled] =
    useState(false)

  useEffect(() => {
    setIsEditProcessModelEnabled(
      orgMemberInfo?.orgMetadata?.editProcessModelEnabled ?? false
    )
  }, [orgMemberInfo])

  const [validationErrors, setValidationErrors] = useState({
    nodes: new Set(),
    edges: new Set(),
  })

  const validateNodeConnections = useCallback(
    (nodeId) => {
      const node = nodes.find((n) => n.id === nodeId)
      if (!node) return false

      const nodeType = node.data.nodeType.toLowerCase()
      const rules = nodeTypeRules[nodeType]
      if (!rules) return true

      const incomingEdges = edges.filter((e) => e.target === nodeId)
      const outgoingEdges = edges.filter((e) => e.source === nodeId)

      return !(
        (rules.minInputs !== undefined &&
          incomingEdges.length < rules.minInputs) ||
        (rules.maxInputs !== undefined &&
          incomingEdges.length > rules.maxInputs) ||
        (rules.minOutputs !== undefined &&
          outgoingEdges.length < rules.minOutputs) ||
        (rules.maxOutputs !== undefined &&
          outgoingEdges.length > rules.maxOutputs)
      )
    },
    [nodes, edges]
  )

  const getNodeValidationError = (nodeId, nodes, edges) => {
    const node = nodes.find((n) => n.id === nodeId)
    if (!node) return ''

    const nodeType = node.data.nodeType.toLowerCase()
    const rules = nodeTypeRules[nodeType]
    if (!rules) return ''

    const incomingEdges = edges.filter((e) => e.target === nodeId)
    const outgoingEdges = edges.filter((e) => e.source === nodeId)

    if (
      rules.minInputs !== undefined &&
      incomingEdges.length < rules.minInputs
    ) {
      return `${node.data.label} (${nodeType}) requires at least ${rules.minInputs} incoming connections`
    }
    if (
      rules.maxInputs !== undefined &&
      incomingEdges.length > rules.maxInputs
    ) {
      return `${node.data.label} (${nodeType}) cannot have more than ${rules.maxInputs} incoming connections`
    }
    if (
      rules.minOutputs !== undefined &&
      outgoingEdges.length < rules.minOutputs
    ) {
      return `${node.data.label} (${nodeType}) requires at least ${rules.minOutputs} outgoing connections`
    }
    if (
      rules.maxOutputs !== undefined &&
      outgoingEdges.length > rules.maxOutputs
    ) {
      return `${node.data.label} (${nodeType}) cannot have more than ${rules.maxOutputs} outgoing connections`
    }
    return ''
  }

  const getEdgeValidationError = (edgeId, nodes, edges) => {
    const edge = edges.find((e) => e.id === edgeId)
    if (!edge) return ''

    const sourceNode = nodes.find((n) => n.id === edge.source)
    const targetNode = nodes.find((n) => n.id === edge.target)

    if (!sourceNode || !targetNode) {
      return 'Invalid connection: Source or target node not found'
    }
    if (sourceNode.data.productId !== targetNode.data.productId) {
      return `Invalid connection between ${sourceNode.data.label} and ${targetNode.data.label}: Nodes must belong to the same product`
    }
    return ''
  }

  const validateAllConnections = useCallback(() => {
    const nodeErrors = new Set()
    const edgeErrors = new Set()

    const errorMessages = []

    nodes.forEach((node) => {
      const error = getNodeValidationError(node.id, nodes, edges)
      if (error) {
        nodeErrors.add(node.id)
        errorMessages.push(error)
      }
    })

    edges.forEach((edge) => {
      const error = getEdgeValidationError(edge.id, nodes, edges)
      if (error) {
        edgeErrors.add(edge.id)
        errorMessages.push(error)
      }
    })

    setValidationErrors({ nodes: nodeErrors, edges: edgeErrors })
    return { isValid: errorMessages.length === 0, errorMessages }
  }, [nodes, edges, validateNodeConnections])

  const [hasChanges, setHasChanges] = useState(false)
  const [isUpdateProductLoading, setIsUpdateProductLoading] = useState(false)

  const [createProduct, { loading: createProductIsLoading }] = useMutation(
    CREATE_PRODUCT_MUTATION
  )

  const [createSupplier, { loading: createSupplierIsLoading }] = useMutation(
    CREATE_SUPPLIER_MUTATION
  )

  const [
    createProductProcessModel,
    { loading: createProductProcessModelIsLoading },
  ] = useMutation(CREATE_PRODUCT_PROCESS_MODEL_MUTATION)

  const [deleteProduct, { loading: deleteProductLoading }] = useMutation(
    DELETE_PRODUCT_MUTATION
  )

  const [backupProduct, { loading: backupProductIsLoading }] = useMutation(
    BACKUP_PRODUCT_MUTATION
  )

  const [restoreProduct, { loading: restoreProductIsLoading }] = useMutation(
    RESTORE_PRODUCT_MUTATION
  )

  const [activateProduct, { loading: activateProductIsLoading }] = useMutation(
    ACTIVATE_PRODUCT_MUTATION
  )

  const [createEmissionsFactor, { loading: createEmissionsFactorIsLoading }] =
    useMutation(CREATE_EMISSIONS_FACTOR_MUTATION)

  const [selectedEdge, setSelectedEdge] = useState(null)

  const handleEdgeClick = (event, edge) => {
    event.stopPropagation()
    setSelectedEdge(edge.id)
  }

  const handlePaneClick = () => {
    setSelectedEdge(null)
  }

  const handleDeleteEdge = useCallback(
    (edgeId) => {
      setEdges((edges) => edges.filter((edge) => edge.id !== edgeId))
      setSelectedEdge(null)
      setHasChanges(true)
    },
    [setEdges]
  )

  const nodeContext = useMemo(
    () => ({
      setNodes: (updater) => {
        setNodes(updater)
        setHasChanges(true)
      },
      setEdges,
      editProcessModelEnabled,
      userMetadata,
    }),
    [setNodes, setEdges, editProcessModelEnabled, userMetadata]
  )

  const nodeTypes = useMemo(
    () => ({
      custom: _CustomNode,
    }),
    []
  )

  const edgeTypes = useMemo(
    () => ({
      default: (props) => (
        <CustomEdge
          {...props}
          data={{ onDelete: handleDeleteEdge, context: nodeContext }}
        />
      ),
    }),
    [handleDeleteEdge, nodeContext]
  )

  const handleCreateEmissionsFactor = async (
    emissionsFactor,
    activityType = 'Raw Materials'
  ) => {
    const emissionsFactorInput = {
      parent_emissions_factor: {
        activity_name: emissionsFactor.activityName,
        activity_type: activityType,
        unit: emissionsFactor.unit,
        reference_product_name: emissionsFactor.referenceProduct,
        geography: emissionsFactor.geography,
        source: emissionsFactor.source,
        kg_co2e: emissionsFactor.kgCO2e,
      },
      exchanges: emissionsFactor.exchanges.map((exchange) => {
        return {
          exchange_name: exchange.exchangeName,
          amount: exchange.amount,
          unit: exchange.unit,
          input_stream: exchange.inputStream,
          exchange_emissions_factor: {
            activity_name: exchange.exchangeEmissionsFactor.activityName,
            activity_type:
              exchange.exchangeEmissionsFactor.activityType ?? activityType,
            reference_product_name:
              exchange.exchangeEmissionsFactor.referenceProduct,
            geography: exchange.exchangeEmissionsFactor.geography,
            source: exchange.exchangeEmissionsFactor.source,
            unit: exchange.exchangeEmissionsFactor.unit,
          },
        }
      }) ?? [],
      elemental_ef_values: emissionsFactor.elementalEfValues?.map((ef) => {
        return {
          lcia_method: ef.lciaMethod,
          impact_category_name: ef.impactCategoryName,
          impact_category_indicator: ef.impactCategoryIndicator,
          impact_category_unit: ef.impactCategoryUnit,
          amount: ef.amount,
        }
      }) ?? []
    }

    try {
      const response = await createEmissionsFactor({
        variables: {
          emissionsFactor: emissionsFactorInput,
        },
      })

      return response.data
    } catch (error) {
      console.error('Error creating emissions factor:', error)
      return {
        emissionsFactor: {
          createEmissionsFactor: emissionsFactor,
        },
      }
    }
  }

  const handleCreateProduct = async (product) => {
    let createProductInput = product.productType === 'product' ? {
      product_id: product.productId,
      product_name: product.productName,
      product_type: 'product',
      brand: product.brand,
      country_of_use: product.countryOfUse,
      factory_country: product.factoryCountry,
      factory_city: product.factoryCity,
      primary_category: product.category,
      functional_unit: product.functionalUnit
        ? `${product.functionalUnit}${product.functionalUnitUnit}`
        : null,
      product_image: product.productImage ?? null,
      annual_sales_volume_units:
        parseInt(product.annualSalesVolumeUnits) ?? null,
      tags: product.tags,
    } : {
        product_id: product.productId,
        product_name: product.productName,
        product_type: 'component',
        tags: product.tags,
    }

    const response = await createProduct({
      variables: {
        product: createProductInput,
      },
    })

    return response.data
  }

  const handleActivateProduct = async (productId) => {
    const response = await activateProduct({
      variables: {
        productId: productId,
      },
    })

    return response.data
  }

  const handleCreateProcessModel = async (productId) => {
    const _nodes = []
    for (const node of nodes) {
      let emissionsFactorMatch = node.data.emissionsFactor
      try {

        if (emissionsFactorMatch?.modified) {
          const emissionsFactor = await handleCreateEmissionsFactor(
            emissionsFactorMatch
          )
          if (emissionsFactor.createEmissionsFactor) {
            emissionsFactorMatch = emissionsFactor.createEmissionsFactor
          }
        }
      } catch (error) {
        console.error('Error creating emissions factor:', error)
      } finally {
        _nodes.push({
          id: parseInt(node.id),
          name: node.data.name,
          component_name: node.data.component,
          description: node.data.description,
          node_type: node.data.nodeType,
          recycled_content_rate: node.data.recycledContent,
          location: node.data.location ? {
            city: node.data.location?.city,
            country: node.data.location?.country,
          } : null,
          emissions_factor: node.data.emissionsFactor
            ? {
                activity_name: node.data.emissionsFactor.activityName,
                reference_product_name:
                  node.data.emissionsFactor.referenceProduct,
                geography: node.data.emissionsFactor.geography,
                source: node.data.emissionsFactor.source,
              }
            : null,
          amount: parseFloat(node.data.amount) || 0,
          unit: node.data.unit,
          quantity: parseInt(node.data.quantity) || 1,
          supplier_id: node.data.supplier?.id ?? null,
          mass_allocation_per_kg: node.data.massAllocationPerKg,
          eol_recycling_rate: parseFloat(node.data.recyclingDisposalRate) / 100,
          eol_landfill_rate: parseFloat(node.data.landfillDisposalRate) / 100,
          eol_incineration_rate: parseFloat(node.data.incinerationDisposalRate) / 100,
          eol_composting_rate: parseFloat(node.data.compostingDisposalRate) / 100,
          scrap_rate: node.data.scrapRate,
          scrap_fate: node.data.scrapFate,
        })
      }
    }

    const _edges = edges.map((edge) => {
      const fromNode = nodes.find((node) => node.id === edge.source)
      const toNode = nodes.find((node) => node.id === edge.target)
      return {
        from_node_id: parseInt(fromNode.data.id),
        to_node_id: parseInt(toNode.data.id),
      }
    })

    const response = await createProductProcessModel({
      variables: {
        productId: productId,
        processModel: {
          nodes: _nodes,
          edges: _edges,
        },
      },
    })

    return response.data
  }

  const nodesWithValidation = useMemo(
    () =>
      nodes.map((node) => ({
        ...node,
        style: {
          ...node.style,
          border: validationErrors.nodes.has(node.id)
            ? '2px solid #ff4d4f'
            : '1px solid #ccc',
        },
      })),
    [nodes, validationErrors.nodes]
  )

  const edgesWithValidation = useMemo(
    () =>
      edges.map((edge) => ({
        ...edge,
        style: {
          ...edge.style,
          stroke: validationErrors.edges.has(edge.id) ? '#ff4d4f' : '#bfbfbf',
        },
      })),
    [edges, validationErrors.edges]
  )

  const handleUpdateProduct = async () => {
    const validation = validateAllConnections()
    if (!validation.isValid) {
      notification.error({
        message: 'Validation Errors',
        description: (
          <ul className="list-disc pl-4">
            {validation.errorMessages.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        ),
      })
      return
    }

    let productId = null
    setIsUpdateProductLoading(true)
    try {
      await backupProduct({
        variables: {
          productId: data.productId,
        },
      })

      const productResponse = await handleCreateProduct(data)
      productId = productResponse.createProduct.productId

      await handleCreateProcessModel(productId)

      await handleActivateProduct(productId)

      notification.success({
        placement: 'topRight',
        message: 'Updated Product',
        description: `${productResponse.createProduct.productName} updated successfully`,
      })

      await deleteProduct({
        variables: {
          productId: `${data.productId}_backup`,
        },
      })

      setTimeout(() => {
        window.location.reload()
      }, 2000)
    } catch (error) {
      console.error('Error updating product:', error)
      if (productId) {
        await deleteProduct({
          variables: { productId: productId },
        })
      }

      await restoreProduct({
        variables: {
          productId: `${data.productId}_backup`,
        },
      })
      const errorMessage =
        error.graphQLErrors?.[0]?.extensions?.originalError?.message ??
        error.message

      return notification.error({
        placement: 'topRight',
        message: 'Error updating product',
        description: errorMessage,
      })
    } finally {
      setIsUpdateProductLoading(false)
    }
  }

  const createNodesAndEdges = useCallback(
    (graphData) => {
      return baseCreateNodesAndEdges(graphData, nodeContext)
    },
    [nodeContext]
  )

  const { nodes: memoNodes, edges: memoEdges } = useMemo(() => {
    const { nodes, edges } = createNodesAndEdges(data)
    return getLayoutedElements(nodes, edges)
  }, [data, createNodesAndEdges])

  useEffect(() => {
    setNodes(memoNodes)
    setEdges(memoEdges)
  }, [memoNodes, memoEdges, setNodes, setEdges])

  const onConnect = useCallback(
    (params) => {
      const sourceNode = nodes.find((n) => n.id === params.source)
      const targetNode = nodes.find((n) => n.id === params.target)

      if (!sourceNode || !targetNode) return

      const sourceNodeType = sourceNode.data.nodeType.toLowerCase()
      const targetNodeType = targetNode.data.nodeType.toLowerCase()

      const sourceOutgoing = edges.filter((e) => e.source === params.source)
      const targetIncoming = edges.filter((e) => e.target === params.target)

      if (
        nodeTypeRules[sourceNodeType]?.maxOutputs &&
        sourceOutgoing.length >= nodeTypeRules[sourceNodeType].maxOutputs
      ) {
        notification.error({
          message: 'Connection Error',
          description: `${sourceNodeType} node can't have more than ${nodeTypeRules[sourceNodeType].maxOutputs} outgoing connections`,
        })
        return
      }

      if (
        nodeTypeRules[targetNodeType]?.maxInputs &&
        targetIncoming.length >= nodeTypeRules[targetNodeType].maxInputs
      ) {
        notification.error({
          message: 'Connection Error',
          description: `${targetNodeType} node can't have more than ${nodeTypeRules[targetNodeType].maxInputs} incoming connections`,
        })
        return
      }

      setEdges((eds) =>
        addEdge(
          {
            ...params,
            type: 'default',
            style: { stroke: '#606060', strokeWidth: 5 },
            markerEnd: {
              type: 'arrowclosed',
              width: 15,
              height: 15,
              color: '#606060',
            },
          },
          eds
        )
      )
      setHasChanges(true)
    },
    [nodes, edges, setEdges]
  )

  const defaultEdgeOptions = useMemo(
    () => ({
      type: 'default',
      animated: true,
      style: {
        stroke: '#bfbfbf',
        strokeWidth: 2,
        strokeDasharray: '5 5',
      },
      markerEnd: {
        type: 'arrowclosed',
        width: 15,
        height: 15,
        color: '#bfbfbf',
      },
    }),
    []
  )

  return (
    <div style={{ width: '100%', height: '900px', position: 'relative' }}>
      {hasChanges ? (
        <Button
          type="primary"
          onClick={handleUpdateProduct}
          loading={isUpdateProductLoading}
          style={{
            display: editProcessModelEnabled ? 'block' : 'none',
            position: 'absolute',
            top: 20,
            right: 20,
            zIndex: 5,
          }}
        >
          Save Changes
        </Button>
      ) : null}
      <ReactFlow
        nodes={nodesWithValidation}
        edges={edgesWithValidation}
        onEdgeClick={handleEdgeClick}
        onPaneClick={handlePaneClick}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        nodesDraggable={true}
        nodesConnectable={true}
        elementsSelectable={true}
        edgesUpdatable={true}
        edgesFocusable={true}
        selectNodesOnDrag={false}
        fitView
        fitViewOptions={{
          padding: 0.2,
          minZoom: 0.3,
          maxZoom: 1.5,
        }}
        proOptions={{ hideAttribution: true }}
        defaultEdgeOptions={defaultEdgeOptions}
        connectionMode="straight"
      >
        <Background />
        <Controls />
        <MiniMap
          nodeStrokeColor={(n) => nodeTypeColors[n.data?.nodeType] || '#d9d9d9'}
          nodeColor={(n) => nodeTypeColors[n.data?.nodeType] || '#fff'}
          style={{
            backgroundColor: '#f0f2f5',
          }}
          zoomable
          pannable
        />
      </ReactFlow>
    </div>
  )
}

export default GraphModel
