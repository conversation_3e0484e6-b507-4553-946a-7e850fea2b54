import React, { useState, useEffect } from 'react'
import {
  Card,
  Col,
  Row,
  Typography,
  Space,
  Input,
  Button,
  Tag,
  Tooltip,
  Table,
  message,
  Progress,
  Divider,
  Collapse,
  CollapseProps,
  Select,
} from 'antd'
import {
  SearchOutlined,
  RobotOutlined,
  CheckCircleOutlined,
  EnvironmentOutlined,
  LinkOutlined,
} from '@ant-design/icons'
import { useLazyQuery } from '@apollo/client'
import { useAuth } from 'src/auth'
import { PREDICT_EMISSIONS_FACTORS_QUERY } from 'src/utils/graphql'
import LabHelpAndSupport from '../LabHelpAndSupport/LabHelpAndSupport'
import LoadingSkeleton from '../LoadingSkeleton/LoadingSkeleton'
import './style.css'

const { Title, Text, Paragraph } = Typography

// Lifecycle stage options for dropdown - aligned with process model node types
const LIFECYCLE_STAGE_OPTIONS = [
  { value: 'Raw Materials', label: 'Raw Materials' }, // matches 'material' node type
  { value: 'Packaging', label: 'Packaging' }, // matches 'packaging' node type
  { value: 'Manufacturing', label: 'Manufacturing' }, // matches 'production/bundle' node type
  { value: 'Transportation', label: 'Transportation' }, // matches 'transportation' node type
  { value: 'End-of-Life', label: 'End-of-Life' }, // matches 'eol' node type
]

const LabEfMatchingComponent = () => {
  const [searchValue, setSearchValue] = useState('')
  const [selectedLifecycleStage, setSelectedLifecycleStage] = useState(null)
  const [
    predictEmissionsFactors,
    { loading: predictEmissionsFactorsIsLoading },
  ] = useLazyQuery(PREDICT_EMISSIONS_FACTORS_QUERY)
  const [searchResults, setSearchResults] = useState([])
  const { userMetadata } = useAuth()
  const totalSearches = parseInt(
    userMetadata?.user?.metadata?.quota_labs_ef_matching ?? 20
  )

  const [searchesRemaining, setSearchesRemaining] = useState(totalSearches)
  const [expandedRowKeys, setExpandedRowKeys] = useState([])
  const [matchedActivity, setMatchedActivity] = useState(null)

  useEffect(() => {
    if (userMetadata) {
      setSearchesRemaining(
        parseInt(userMetadata?.user?.metadata?.quota_labs_ef_matching ?? 20) -
          (userMetadata?.user?.metadata?.quota_labs_ef_matching_used ?? 0)
      )
    }
  }, [userMetadata])

  const truncateText = (text, maxLength = 200) => {
    if (text && text.length > maxLength) {
      return (
        <span>
          {text.substring(0, maxLength)}
          <a style={{ marginLeft: 5 }}>...</a>
        </span>
      )
    }
    return text
  }

  const getConfidenceColor = (confidence) => {
    const colors = { low: 'red', medium: 'orange', high: 'green' }
    return colors[confidence] || 'default'
  }

  const columns = [
    {
      title: 'Activity Name',
      dataIndex: 'activityName',
      key: 'activityName',
      sorter: (a, b) => a.activityName.localeCompare(b.activityName),
      searchable: true,
      width: '30%',
    },
    {
      title: 'Reference Product Description',
      dataIndex: 'productInformation',
      key: 'productInformation',
      render: (text, record) => (
        <span>
          {truncateText(text)}
          {text.length > 200 && (
            <a
              style={{ marginLeft: 5 }}
              onClick={(e) => {
                e.stopPropagation()
                const newExpandedKeys = expandedRowKeys.includes(record.key)
                  ? expandedRowKeys.filter((key) => key !== record.key)
                  : [...expandedRowKeys, record.key]
                setExpandedRowKeys(newExpandedKeys)
              }}
            >
              {expandedRowKeys.includes(record.key) ? 'show less' : 'show more'}
            </a>
          )}
        </span>
      ),
      width: '50%',
      searchable: true,
    },
    {
      title: 'Geography',
      dataIndex: 'geography',
      key: 'geography',
      searchable: true,
      width: '10%',
    },
    {
      title: 'Source',
      dataIndex: 'source',
      key: 'source',
      searchable: true,
      width: '10%',
    },
  ]

  const onSearch = async (value) => {
    if (!userMetadata?.user?.metadata?.test_user) {
      if (parseInt(searchesRemaining) <= 0) {
        message.error(
          'You have reached your search limit. Please contact support to upgrade your plan.'
        )
        return
      }
    }

    const emissionsFactorMatch = {
      activityMatch: null,
      activityMatches: [],
    }
    setMatchedActivity(null)
    setSearchResults([])

    try {
      let emissionsFactorMatchGeography = 'GLO'

      const response = await predictEmissionsFactors({
        variables: {
          chemicalName: value,
          productCategory: null,
          casNo: null,
          geography: emissionsFactorMatchGeography,
          geographyModeling: false,
          unit: null,
          labs: true,
          lcaLifecycleStage: selectedLifecycleStage,
        },
      })

      if (
        response?.data?.predictEmissionsFactors?.recommendations
          ?.length
      ) {
        emissionsFactorMatch.activityMatch = {
          ...(response?.data?.predictEmissionsFactors
            ?.matchedActivity ?? emissionsFactorMatch.activityMatches[0]),
          ...(response?.data?.predictEmissionsFactors?.confidence && {
            confidence:
              response.data.predictEmissionsFactors.confidence,
          }),
          ...(response?.data?.predictEmissionsFactors
            ?.explanation && {
            explanation:
              response.data.predictEmissionsFactors.explanation,
          }),
        }

        setMatchedActivity(emissionsFactorMatch.activityMatch)

        emissionsFactorMatch.activityMatches =
          response?.data?.predictEmissionsFactors.recommendations
            .filter((x) => {
              if (
                x.activityUUID === emissionsFactorMatch.activityMatch.activityUUID &&
                x.referenceProduct === emissionsFactorMatch.activityMatch.referenceProduct &&
                x.geography === emissionsFactorMatch.activityMatch.geography
              ) {
                return false
              } else {
                return true
              }
            })
            .map((x, index) => {
              return {
                key: index + 1,
                ...x,
              }
            })

        setSearchesRemaining((prev) => Math.max(0, prev - 1))
      }
    } catch (error) {
      message.error('Failed to predict emissions factors')
      console.error('Error predicting emissions factors:', error)
    } finally {
      setSearchResults(emissionsFactorMatch.activityMatches)
    }
  }

  const searchQuotaProgress = (
    <Tooltip
      title={`${searchesRemaining} out of ${totalSearches} searches remaining`}
    >
      <Space>
        <Progress
          type="circle"
          percent={Math.round((searchesRemaining / totalSearches) * 100)}
          width={40}
          format={() => `${searchesRemaining}/${totalSearches}`}
        />
        <Text>Searches Remaining</Text>
      </Space>
    </Tooltip>
  )

  const otherResultsCollapseContent: CollapseProps['items'] = [
    {
      key: '1',
      label: <Title level={4}>Other Results</Title>,
      children: (
        <Table
          dataSource={searchResults}
          columns={columns}
          pagination={{
            pageSize: 10,
          }}
          style={{ marginTop: 20 }}
          loading={predictEmissionsFactorsIsLoading}
          expandable={{
            expandedRowRender: (record) => (
              <p style={{ margin: 0 }}>{record.productInformation}</p>
            ),
            expandedRowKeys: expandedRowKeys,
            onExpand: (expanded, record) => {
              const newExpandedKeys = expanded
                ? [...expandedRowKeys, record.key]
                : expandedRowKeys.filter((key) => key !== record.key)
              setExpandedRowKeys(newExpandedKeys)
            },
          }}
        />
      ),
    },
  ]

  return (
    <Row gutter={[24, 24]}>
      <Col span={18}>
        <Card
          title={
            <Space>
              <RobotOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
              <Title level={3} style={{ margin: 0 }}>
                AI Emission Factor Matching Tool
              </Title>
            </Space>
          }
          extra={searchQuotaProgress}
        >
          <Paragraph>
            Match your product raw materials with our advanced AI-powered
            Emissions Factor (EF) Matching Tool. Simply enter your material or
            process, and let our AI find the most accurate emissions factor.
          </Paragraph>

          <Space direction="vertical" style={{ width: '100%', marginBottom: '20px' }}>
            <div>
              <Text strong>Lifecycle Stage (Optional):</Text>
              <Select
                placeholder="Select lifecycle stage for better matching"
                value={selectedLifecycleStage}
                onChange={setSelectedLifecycleStage}
                options={LIFECYCLE_STAGE_OPTIONS}
                allowClear
                style={{ width: '100%', marginTop: '8px' }}
              />
            </div>
          </Space>

          <Input.Search
            placeholder="Enter material or process (e.g., 'citric acid, steel production')"
            enterButton={
              <Button id="search-btn" type="primary" icon={<SearchOutlined />}>
                Search
              </Button>
            }
            id="search"
            size="large"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onSearch={onSearch}
            style={{ marginBottom: '20px' }}
            loading={predictEmissionsFactorsIsLoading}
          />

          {predictEmissionsFactorsIsLoading && <LoadingSkeleton />}

          {matchedActivity && (
            <>
              <Title level={4}>Top Match</Title>
              <Card
                style={{
                  marginBottom: 20,
                  borderColor: '#52c41a',
                  backgroundColor: '#f6ffed',
                }}
              >
                <Row gutter={[16, 16]}>
                  <Col span={24}>
                    <Title level={4}>{matchedActivity.activityName}</Title>
                    <Tag color={getConfidenceColor(matchedActivity.confidence)}>
                      {matchedActivity.confidence.toUpperCase()} CONFIDENCE
                    </Tag>
                  </Col>
                  <Col span={24}>
                    <Text strong>Explanation: </Text>
                    <Text>{matchedActivity.explanation}</Text>
                  </Col>
                  <Col span={12}>
                    <Space>
                      <EnvironmentOutlined />
                      <Text strong>Geography: </Text>
                      <Text>{matchedActivity.geography}</Text>
                    </Space>
                  </Col>
                  <Col span={12}>
                    <Space>
                      <LinkOutlined />
                      <Text strong>Source: </Text>
                      <Text>{matchedActivity.source}</Text>
                    </Space>
                  </Col>
                  <Col span={24}>
                    <Text strong>Reference Product Description: </Text>
                    <Paragraph
                      ellipsis={{
                        rows: 2,
                        expandable: true,
                        symbol: 'show more',
                      }}
                    >
                      {matchedActivity.productInformation}
                    </Paragraph>
                  </Col>
                </Row>
              </Card>
            </>
          )}

          <Collapse
            style={{ textAlign: 'left' }}
            ghost
            items={otherResultsCollapseContent}
          />
        </Card>
      </Col>
      <Col span={6}>
        <LabHelpAndSupport userMetadata={userMetadata} />
      </Col>
    </Row>
  )
}

export default LabEfMatchingComponent
