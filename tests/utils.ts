import { CONST } from './const'

async function login(page) {
  await page.goto(CONST.baseURL)
  await page.waitForURL(CONST.loginPageURL)
  await page.getByRole('textbox', { name: 'Email' }).click()
  await page.getByRole('textbox', { name: 'Email' }).fill(CONST.testUserEmail)
  await page.getByRole('textbox', { name: 'Password' }).click()
  await page
    .getByRole('textbox', { name: 'Password' })
    .fill(CONST.testUserPassword)
  await page.getByRole('button', { name: 'Log in with email' }).click()
  await page.waitForSelector('text=Welcome!')
  await page.evaluate(() => {
    let tourPropsJson = {
      tourCompleted: true,
    }
    localStorage.setItem('tourProps', JSON.stringify(tourPropsJson))
  })
  await page.goto(`${CONST.baseURL}/dashboard`)
  await page.waitForSelector('text=Total Product Emissions')
  await page.context().storageState({ path: CONST.authFile })
}

async function processModelLogin(page) {
  await page.goto(CONST.baseURL)
  await page.waitForURL(CONST.loginPageURL)
  await page.getByRole('textbox', { name: 'Email' }).click()
  await page.getByRole('textbox', { name: 'Email' }).fill(CONST.testTrialUserEmail)
  await page.getByRole('textbox', { name: 'Password' }).click()
  await page
    .getByRole('textbox', { name: 'Password' })
    .fill(CONST.testTrialUserPassword)
  await page.getByRole('button', { name: 'Log in with email' }).click()
  await page.waitForSelector('text=Welcome!')
  await page.evaluate(() => {
    let tourPropsJson = {
      tourCompleted: true,
    }
    localStorage.setItem('tourProps', JSON.stringify(tourPropsJson))
  })
  await page.goto(`${CONST.baseURL}/dashboard`)
  await page.waitForSelector('text=Total Product Emissions')
  await page.context().storageState({ path: CONST.authFile })
}

async function trialUserLogin(page) {
  await page.goto(CONST.baseURL)
  await page.waitForURL(CONST.loginPageURL)
  await page.getByRole('textbox', { name: 'Email' }).click()
  await page
    .getByRole('textbox', { name: 'Email' })
    .fill(CONST.testTrialUserEmail)
  await page.getByRole('textbox', { name: 'Password' }).click()
  await page
    .getByRole('textbox', { name: 'Password' })
    .fill(CONST.testTrialUserPassword)
  await page.getByRole('button', { name: 'Log in with email' }).click()
  await page.waitForSelector('text=Welcome!')
  await page.evaluate(() => {
    let tourPropsJson = {
      tourCompleted: true,
    }
    localStorage.setItem('tourProps', JSON.stringify(tourPropsJson))
  })
  await page.goto(`${CONST.baseURL}/dashboard`)
  await page.waitForSelector('text=Total Product Emissions')
  await page.context().storageState({ path: CONST.authFile })
}

async function labUserLogin(page) {
  await page.goto(CONST.baseURL)
  await page.waitForURL(CONST.loginPageURL)
  await page.getByRole('textbox', { name: 'Email' }).click()
  await page
    .getByRole('textbox', { name: 'Email' })
    .fill(CONST.testLabUserEmail)
  await page.getByRole('textbox', { name: 'Password' }).click()
  await page
    .getByRole('textbox', { name: 'Password' })
    .fill(CONST.testLabUserPassword)
  await page.getByRole('button', { name: 'Log in with email' }).click()
  await page.waitForSelector('text=Welcome to Labs')
  await page.context().storageState({ path: CONST.authFile })
}

export { login, trialUserLogin, labUserLogin, processModelLogin }
