import { test, expect, Request } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
})

test.use({ storageState: CONST.authFile })

const requestTimings = new Map<Request, number>();

test('Add Product From File', async ({ page }) => {
  test.setTimeout(480000)

  page.on('request', (request) => {
    requestTimings.set(request, Date.now());
  });

  page.on('console', msg => {
    console.log(`[Console ${msg.type()}] ${msg.text()}`);
  });

  page.on('response', async (response) => {
    console.log('Response URL:', response.url());
    console.log('Response Status:', response.status());

    const request = response.request();
    const startTime = requestTimings.get(request);
    if (startTime) {
      const latency = Date.now() - startTime;
      console.log(`Response Time: ${latency}ms`);
      requestTimings.delete(request);
    }

    if (response.url().includes('/graphql')) {
      try {
        const responseBody = await response.json();
        console.log('GraphQL Response:', JSON.stringify(responseBody));
      } catch (error) {
        console.error('Error parsing response:', error)
      }
    }
  })

  await page.screenshot({
    path: 'tests/screenshots/apff_init.png',
  })
  await page.goto(`${CONST.baseURL}/product/import`)
  await page.screenshot({
    path: 'tests/screenshots/apff_import_page.png',
  })

  await page.context().storageState({ path: CONST.authFile })

  try {
    await page.waitForSelector('text=Upload File', { timeout: 220000 })
    await page.screenshot({
      path: 'tests/screenshots/apff_import_upload.png',
    })
  } catch (error) {
    await page.screenshot({
      path: 'tests/screenshots/apff_import_upload_error.png',
    })
    throw error
  }

  const input = await page.$('input[type="file"]')

  await input?.setInputFiles('tests/files/Vibrant_Colors_Laundry_Plus_BOM.pdf')

  await page.waitForSelector(
    'text=Review your uploaded file and click on confirm to proceed',
    { timeout: 60000 }
  )

  await page.click('button:has-text("Confirm")')

  const productNameInput = page.locator('input#productName')
  await expect(productNameInput).toHaveValue('Vibrant Colors Laundry Plus', {
    timeout: 120000,
  })

  await page.waitForTimeout(1000)

  //wait for category prediction loading spinner to disappear
  await page.waitForFunction(
    () =>
      !document
        .querySelector('button#predictProductCategoryButton')
        ?.classList.contains('ant-btn-loading')
  )

  await page.getByRole('button', { name: 'right Optional Data' }).click();

  await page.getByPlaceholder('1.5L').fill('1L');

  await page.click('button:has-text("Next")')

  //wait for loading spinner to disappear
  await page.waitForFunction(
    () =>
      !document
        .querySelector('button#next-button')
        ?.classList.contains('ant-btn-loading')
  )

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=Predicted', { timeout: 120000 });

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=Factory to Retail', { timeout: 5000 })

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=Consumer use', { timeout: 1000 })

  await page.click('button:has-text("Next")')

  await page.waitForSelector('text=End of life', { timeout: 1000 })

  await page.click('button:has-text("Next")')

  //wait for loading spinner to disappear
  await page.waitForFunction(
    () =>
      !document
        .querySelector('button#finish-button')
        ?.classList.contains('ant-btn-loading')
  )

  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(15000);

  //Submit
  await page.click('button:has-text("Save")')

  await page.context().storageState({ path: CONST.authFile })

  await page.screenshot({
    path: 'tests/screenshots/apff_pre_create.png',
  })

  await page.waitForSelector(
    `text=Product Vibrant Colors Laundry Plus created successfully`,
    { timeout: 420000 }
  )

  await page.screenshot({
    path: 'tests/screenshots/apff_post_create.png',
  })

  await page.context().storageState({ path: CONST.authFile })

  await page.waitForURL(`${CONST.baseURL}/products`)
})
